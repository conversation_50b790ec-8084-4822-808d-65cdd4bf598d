import { Box, CssBaseline, ThemeProvider, createTheme } from "@mui/material";
import Header from "./components/Header";
import Sidebar from "./components/Sidebar";
import MainContent from "./components/MainContent";
import RightSidebar from "./components/RightSidebar";

const theme = createTheme({
  palette: {
    mode: "light",
    primary: {
      main: "#1976d2",
    },
    background: {
      default: "#f5f5f5",
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ display: "flex", height: "100vh" }}>
        {/* Left Sidebar */}
        <Sidebar />

        {/* Main Content Area */}
        <Box sx={{ display: "flex", flexDirection: "column", flex: 1 }}>
          {/* Header */}
          <Header />

          {/* Main Content */}
          <MainContent />
        </Box>

        {/* Right Sidebar */}
        <RightSidebar />
      </Box>
    </ThemeProvider>
  );
}

export default App;

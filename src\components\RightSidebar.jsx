import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Chip,
} from "@mui/material";

function RightSidebar() {
  const notifications = ["Text", "Text", "Text", "Text", "Text", "Text"];

  const activities = ["Text", "Text", "Text", "Text", "Text", "Text"];

  const contacts = [
    { name: "<PERSON><PERSON>" },
    { name: "<PERSON>" },
    { name: "Andi <PERSON>" },
    { name: "<PERSON><PERSON>um<PERSON>" },
    { name: "<PERSON>" },
    { name: "<PERSON>" },
  ];

  return (
    <Box
      sx={{
        width: 230,
        bgcolor: "background.paper",
        borderLeft: 1,
        borderColor: "divider",
        height: "100%",
        overflow: "auto",
        p: 1,
        scrollbarWidth: "none",
      }}
    >
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Notifications
        </Typography>
        <List dense>
          {notifications.map((activity, index) => (
            <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
              <ListItemText primary={activity} />
            </ListItem>
          ))}
        </List>
      </Box>
      <Divider sx={{ my: 2 }} />
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Activities
        </Typography>

        <List dense>
          {activities.map((activity, index) => (
            <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
              <ListItemText primary={activity} />
            </ListItem>
          ))}
        </List>
      </Box>

      <Divider sx={{ my: 2 }} />

      <Box>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: "bold" }}>
          Contacts
        </Typography>

        <List dense>
          {contacts.map((contact, index) => (
            <ListItem key={index} sx={{ px: 0, py: 1 }}>
              <ListItemAvatar>
                <Box sx={{ position: "relative" }}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      bgcolor: "primary.main",
                      fontSize: "0.875rem",
                    }}
                  >
                    {contact.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </Avatar>
                </Box>
              </ListItemAvatar>
              <ListItemText primary={contact.name} />
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );
}

export default RightSidebar;

import { Box, Typography } from "@mui/material";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Cell,
} from "recharts";

const data = [
  { month: "Jan", value: 35000 },
  { month: "Feb", value: 35000 },
  { month: "Mar", value: 35000 },
  { month: "Apr", value: 29000 },
  { month: "May", value: 35000 },
  { month: "Jun", value: 35000 },
  { month: "Jul", value: 35000 },
  { month: "Aug", value: 35000 },
  { month: "Sep", value: 35000 },
  { month: "Oct", value: 35000 },
  { month: "Nov", value: 35000 },
  { month: "Dec", value: 35000 },
];

function MarketingSeo() {
  return (
    <Box
      sx={{
        bgcolor: "#f9f9f9",
        p: 3,
        borderRadius: 5,
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
        "& *": {
          outline: "none ",
        },
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 3,
          color: "#1e1e1e",
        }}
      >
        Marketing & SEO
      </Typography>

      <Box sx={{ width: "100%", height: 200 }}>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart
            data={data}
            margin={{
              top: 10,
              left: -26,
              // right: 0,
              bottom: 30,
            }}
            barCategoryGap="20%"
          >
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#989898" }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#989898" }}
              tickFormatter={(value) => `${value / 1000}K`}
              domain={[0, 35000]}
              ticks={[0, 10000, 20000, 30000]}
            />
            <Bar dataKey="value" radius={[8, 8, 8, 8]}>
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={
                    entry.month === "Apr" || entry.month === "Oct"
                      ? "#92bfff"
                      : "#000"
                  }
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
}

export default MarketingSeo;

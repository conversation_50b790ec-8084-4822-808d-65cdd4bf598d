import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Badge,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  DarkMode,
  LightMode,
} from "@mui/icons-material";
import { useState } from "react";

function Header() {
  const [darkMode, setDarkMode] = useState(false);

  return (
    <AppBar position="static" color="default" elevation={1}>
      <Toolbar
        sx={{
          justifyContent: "space-between",
          minHeight: "64px",
          bgcolor: darkMode ? "#2a2a2a" : "#fff",
          borderBottom: 1,
          borderColor: darkMode ? "#333" : "divider",
          color: darkMode ? "#fff" : "#000",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Tab label="Text" sx={{ color: darkMode ? "#fff" : "#000" }} />
          <Tab label="Text" sx={{ color: darkMode ? "#fff" : "#000" }} />
          <Tab label="Text" sx={{ color: darkMode ? "#fff" : "#000" }} />
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            gap: 2,
          }}
        >
          <TextField
            size="small"
            placeholder="Search"
            variant="outlined"
            sx={{
              width: 200,
              border: "none",
              bgcolor: "#f5f5f5",

              "& .MuiOutlinedInput-root": {
                backgroundColor: darkMode ? "#333" : "#fff",
                color: darkMode ? "#fff" : "#000",
              },
              "& .MuiInputBase-input::placeholder": {
                color: darkMode ? "#fff" : "#000",
                opacity: 1,
              },
            }}
            // InputProps={{
            //   startAdornment: (
            //     <InputAdornment position="start">
            //       <SearchIcon
            //         fontSize="small"
            //         sx={{ color: darkMode ? "#fff" : "#666" }}
            //       />
            //     </InputAdornment>
            //   ),
            // }}
          />

          <Typography variant="body2" sx={{ color: darkMode ? "#fff" : "000" }}>
            Text Text Text Text
          </Typography>

          <IconButton
            sx={{
              p: 0,
              color: darkMode ? "#fff" : "#000",
              "& svg": {
                width: "20px",
                height: "20px",
              },
            }}
            color="inherit"
            onClick={() => setDarkMode(!darkMode)}
          >
            {darkMode ? <LightMode /> : <DarkMode />}
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
}

export default Header;

import { Box, Typography } from "@mui/material";

function StatsCard({ title, value, percent, icon, bgColor }) {
  const isPositive = parseFloat(percent) >= 0;
  return (
    <Box
      sx={{
        bgcolor: bgColor,
        p: 2,
        borderRadius: 2,
        boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
        width: "200px",
      }}
    >
      <Typography
        sx={{ color: "#1e1e1e", fontWeight: "medium", mb: 1, fontSize: "18px" }}
        variant="h6"
      >
        {title}
      </Typography>

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Box
          sx={{
            color: "#1e1e1e",
            fontWeight: "bold",
            fontSize: "26px",
          }}
        >
          {value}
        </Box>
        <Box
          sx={{
            color: "#1e1e1e",
            fontSize: "14px",
          }}
        >
          {percent}
        </Box>
        <Box
          sx={{
            color: "#1e1e1e",
            fontSize: "14px",
          }}
        >
          {icon}
        </Box>
      </Box>
    </Box>
  );
}

export default StatsCard;

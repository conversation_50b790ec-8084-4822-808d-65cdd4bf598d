const fakeData = {
  chartData: [
    { month: "Jan", value: 30 },
    { month: "Feb", value: 45 },
    { month: "Mar", value: 35 },
    { month: "Apr", value: 50 },
    { month: "May", value: 40 },
    { month: "Jun", value: 60 },
    { month: "Jul", value: 45 },
  ],
  trafficByDevice: [
    { name: "Desktop", value: 45 },
    { name: "<PERSON>", value: 35 },
    { name: "Tablet", value: 20 },
  ],
  trafficByLocation: [
    { name: "United States", value: 32.5, color: "#3B82F6" },
    { name: "Canada", value: 28.7, color: "#10B981" },
    { name: "Mexico", value: 24.3, color: "#F59E0B" },
    { name: "Other", value: 14.5, color: "#EF4444" },
  ],
  seoData: Array.from({ length: 12 }, (_, i) => ({
    name: `KW${i + 1}`,
    value: Math.floor(Math.random() * 40) + 60,
  })),
  notifications: Array.from({ length: 9 }, (_, i) => ({ type: "Text", id: i })),
  contacts: [
    { name: "Name One", avatar: "N" },
    { name: "<PERSON>", avatar: "D" },
    { name: "Bud <PERSON>", avatar: "B" },
    { name: "Fang Giannis", avatar: "F" },
    { name: "Kane <PERSON>", avatar: "K" },
    { name: "Molloy Mary", avatar: "M" },
  ],
  trafficSources: [
    { source: "Google", percentage: 42.3 },
    { source: "YouTube", percentage: 28.1 },
    { source: "Facebook", percentage: 18.7 },
    { source: "Twitter", percentage: 10.9 },
  ],
};
export default fakeData;

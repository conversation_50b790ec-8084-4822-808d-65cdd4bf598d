import { Box, Typography } from "@mui/material";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Cell,
} from "recharts";

const data = [
  {
    name: "Linux",
    value: 18000,
    color: "#9f9ff7",
  },
  {
    name: "<PERSON>",
    value: 30000,
    color: "#96e2d7",
  },
  {
    name: "iOS",
    value: 22000,
    color: "#000",
  },
  {
    name: "Windows",
    value: 35000,
    color: "#91c0fc",
  },
  {
    name: "Android",
    value: 14000,
    color: "#aec8ed",
  },
  {
    name: "Other",
    value: 26000,
    color: "#93e9b8",
  },
];

function TrafficDevice() {
  return (
    <Box
      sx={{
        backgroundColor: "#f9f9f9",
        borderRadius: 5,
        p: 3,
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
        "& *": {
          outline: "none ",
        },
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 3,
          color: "#1e1e1e",
        }}
      >
        Traffic by Device
      </Typography>

      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={data}
          margin={{
            top: 20,
            left: -26,
            // right: 0,
            bottom: 5,
          }}
          barCategoryGap="20%"
        >
          <XAxis
            dataKey="name"
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: "#989898" }}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: "#989898" }}
            tickFormatter={(value) => `${value / 1000}K`}
          />
          <Bar dataKey="value" radius={[8, 8, 8, 8]}>
            {data.map((col, index) => (
              <Cell key={`cell-${index}`} fill={col.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
}

export default TrafficDevice;

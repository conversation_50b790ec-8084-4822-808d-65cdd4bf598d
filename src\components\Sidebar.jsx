import {
  Box,
  List,
  ListItem,
  ListItemText,
  Typography,
  Divider,
} from "@mui/material";

function Sidebar() {
  const menuItems = ["Overview", "Projects"];

  const dashboardItems = ["Content", "Content", "Content"];

  const contentItems = [
    "Content",
    "Content",
    "Content",
    "Content",
    "Content",
    "Content",
    "Content",
    "Content",
    "Content",
    "Content",
  ];

  return (
    <Box
      sx={{
        width: 230,
        bgcolor: "background.paper",
        borderRight: 1,
        borderColor: "divider",
        height: "100%",
        overflow: "auto",
        scrollbarWidth: "none",
      }}
    >
      <Box sx={{ p: 2, borderColor: "divider" }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: "bold" }}>
          DacCau
        </Typography>
      </Box>

      <List>
        {menuItems.map((item, index) => (
          <ListItem
            key={index}
            sx={{
              py: 1,
              px: 2,
              "&:hover": {
                bgcolor: "action.hover",
              },
              ...(item === "Overview" && {
                fontWeight: "bold",
              }),
            }}
          >
            <ListItemText primary={item} />
          </ListItem>
        ))}
      </List>

      <Divider />

      <Box sx={{ px: 2, py: 1 }}>
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ fontWeight: "bold" }}
        >
          Dashboard
        </Typography>
      </Box>

      <List dense>
        {dashboardItems.map((item, index) => (
          <ListItem
            key={index}
            sx={{
              py: 0.5,
              px: 2,
              "&:hover": {
                bgcolor: "action.hover",
              },
            }}
          >
            <ListItemText primary={item} />
          </ListItem>
        ))}
      </List>

      <Divider />

      <Box sx={{ px: 2, py: 1 }}>
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ fontWeight: "bold" }}
        >
          Content
        </Typography>
      </Box>

      <List dense>
        {contentItems.map((item, index) => (
          <ListItem
            key={index}
            sx={{
              py: 0.5,
              px: 2,
              "&:hover": {
                bgcolor: "action.hover",
              },
            }}
          >
            <ListItemText primary={item} />
          </ListItem>
        ))}
      </List>
    </Box>
  );
}

export default Sidebar;

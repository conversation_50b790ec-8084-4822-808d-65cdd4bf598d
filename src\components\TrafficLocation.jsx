import { Box, Typography } from "@mui/material";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from "recharts";

const data = [
  {
    name: "United States",
    value: 52.1,
    color: "#333",
  },
  { name: "Canada", value: 22.8, color: "#93bfff" },
  { name: "Mexico", value: 13.9, color: "#93e9b8" },
  { name: "Other", value: 11.2, color: "#aec8ed" },
];

function TrafficLocation() {
  return (
    <Box
      sx={{
        bgcolor: "#f9f9f9",
        p: 3,
        borderRadius: 5,
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
        "& *": {
          outline: "none ",
        },
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 3,
          color: "#1e1e1e",
        }}
      >
        Traffic by Location
      </Typography>

      <Box sx={{ display: "flex", alignItems: "center", height: "300px" }}>
        <Box sx={{ flex: 1, height: "100%" }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart
              margin={{
                top: 20,
                left: -35,
                bottom: 5,
              }}
            >
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </Box>

        <Box sx={{}}>
          {data.map((item, index) => (
            <Box
              key={index}
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                mb: 2,
                gap: 2,
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Box
                  sx={{
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    bgcolor: item.color,
                    mr: 1,
                  }}
                />

                <Typography
                  sx={{
                    fontSize: "10px",
                    color: "#333",
                    fontWeight: 500,
                  }}
                >
                  {item.name}
                </Typography>
              </Box>
              <Typography
                sx={{
                  fontSize: "14px",
                  color: "#333",
                  fontWeight: "bold",
                }}
              >
                {item.value}%
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
}

export default TrafficLocation;
